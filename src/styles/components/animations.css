/* Shared Animation System - Available to both public and authenticated sections */

/* Enhanced Animation System with Motion One Support */

/* Base animation classes for Motion One integration */
.motion-safe {
  /* Only apply animations if user doesn't prefer reduced motion */
}

@media (prefers-reduced-motion: reduce) {
  .motion-safe {
    animation: none !important;
    transition: none !important;
  }

  /* Disable all Motion One animations */
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Enhanced fade-in animations */
.fade-in-up {
  opacity: 0;
  transform: translateY(20px);
}

.fade-in-down {
  opacity: 0;
  transform: translateY(-20px);
}

.fade-in-left {
  opacity: 0;
  transform: translateX(20px);
}

.fade-in-right {
  opacity: 0;
  transform: translateX(-20px);
}

.fade-in-scale {
  opacity: 0;
  transform: scale(0.95);
}

/* Stagger animation support */
.stagger-container > * {
  opacity: 0;
  transform: translateY(20px);
}

/* Parallax elements */
.parallax-element {
  will-change: transform;
  backface-visibility: hidden;
}

/* Hover animation base classes */
.hover-lift {
  transition: transform 0.2s ease-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale {
  transition: transform 0.2s ease-out;
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Loading animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Scan animation for loading states */
@keyframes scan {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-scan {
  animation: scan 2s linear infinite;
}

/* Page Transition Styles */
.page-content {
  transition: all 0.3s ease-in-out;
  will-change: transform, opacity;
}

.page-content.transitioning {
  pointer-events: none;
}

/* Elements that should animate on page load */
.animate-on-load {
  opacity: 0;
  transform: translateY(20px);
}

/* Page transition overlay */
.page-transition-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--background);
  z-index: 9999;
  pointer-events: none;
}

/* Component transition base classes */
.modal-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Stagger animation support */
.stagger-item {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.4s ease-out;
}

.stagger-item.animate {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced focus states for better transitions */
.focus-transition {
  transition: all 0.2s ease-out;
}

.focus-transition:focus {
  transform: scale(1.02);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* Smooth scrolling for better page transitions */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }

  .page-content,
  .modal-transition,
  .dropdown-transition,
  .stagger-item,
  .focus-transition {
    transition: none !important;
  }
}

/* Fade and blur animations */
@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(24px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes blur-in {
  0% {
    filter: blur(20px);
    opacity: 0;
  }
  100% {
    filter: blur(0);
    opacity: 1;
  }
}

.animate-fade {
  animation: fade-in 0.9s forwards;
}

.animate-blur {
  animation: blur-in 1.2s forwards;
}
