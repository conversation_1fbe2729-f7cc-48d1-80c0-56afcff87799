/* Dashboard and Admin Interface Specific Styles */

/* Dashboard mockup styles */
.dashboard-card {
  background: var(--card);
  border: 2px solid var(--border);
  box-shadow: var(--shadow-lg);
  border-radius: 0.5rem; /* 8px */
}

.metric-card {
  border: 2px solid var(--border);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  border-radius: 0.375rem; /* 6px */
}

.metric-card:hover {
  transform: translate(-1px, -1px);
  box-shadow: 3px 3px 0px 0px hsl(0 0% 0% / 1);
}

/* Custom scrollbar styles for chat containers */
.messages-container {
  scrollbar-width: thin;
  scrollbar-color: var(--border) var(--muted);
}

.messages-container::-webkit-scrollbar {
  width: 8px;
}

.messages-container::-webkit-scrollbar-track {
  background: var(--muted);
  border-left: 2px solid var(--border);
}

.messages-container::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 0;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}

/* Modern full-page chat layout */
.modern-chat-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--background);
}

.modern-messages-area {
  flex: 1;
  width: 100%;
  max-width: 100%;
  padding: 2rem 1.5rem 12rem 1.5rem; /* Increased bottom padding for floating input */
  overflow-y: visible;
  min-height: calc(100vh - 200px); /* Ensure minimum height */
}

.modern-input-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--background);
  border-top: 2px solid var(--border);
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  background: rgba(var(--background-rgb, 255, 255, 255), 0.95);
  z-index: 50;
}

.modern-input-area-seo {
  padding: 2rem 1.5rem; /* Extra padding for SEO filters */
}

/* Auto-scroll behavior for modern chat */
.modern-messages-area {
  scroll-behavior: smooth;
}

/* Responsive design for modern chat */
@media (max-width: 768px) {
  .modern-messages-area {
    padding: 1rem 1rem 8rem 1rem;
  }

  .modern-input-area {
    padding: 1rem;
  }

  .modern-input-area-seo {
    padding: 1.5rem 1rem;
  }
}

@media (max-width: 480px) {
  .modern-messages-area {
    padding: 0.75rem 0.75rem 7rem 0.75rem;
  }

  .modern-input-area {
    padding: 0.75rem;
  }

  .modern-input-area-seo {
    padding: 1rem 0.75rem;
  }
}

/* Message container styling for modern layout */
.modern-message-container {
  max-width: 4xl;
  margin: 0 auto;
  width: 100%;
}

/* Ensure proper spacing between messages in modern layout */
.modern-message-item {
  margin-bottom: 2rem;
}

.modern-message-item:last-child {
  margin-bottom: 4rem; /* Extra space before input area */
}

/* Legacy layout for chat container (keeping for backward compatibility) */
.chat-container {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.messages-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 220px; /* Space for input area */
  overflow-y: auto;
  padding: 1.5rem;
}

.input-wrapper {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 220px;
  background: var(--card);
  border-top: 2px solid var(--border);
  padding: 1.5rem;
}

/* SEO agent specific input wrapper with more height for filters */
.input-wrapper-seo {
  height: 320px; /* Increased to accommodate filters and content */
}

/* SEO agent messages wrapper adjusted for larger input area */
.messages-wrapper-seo {
  bottom: 320px; /* Match the input wrapper height */
}

/* Spotlight-style input */
.spotlight-input-container {
  position: relative;
  border: 2px solid var(--border);
  border-radius: 0.5rem;
  background: var(--background);
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.spotlight-input-container:focus-within {
  border-color: var(--primary);
  box-shadow:
    0 0 0 3px rgba(var(--primary-rgb, 59, 130, 246), 0.1),
    0 0 20px rgba(var(--primary-rgb, 59, 130, 246), 0.1),
    var(--shadow-lg);
  transform: translateY(-2px);
}

.spotlight-input {
  width: 100%;
  background: transparent;
  border: none;
  outline: none;
  font-family: "Plus Jakarta Sans", system-ui, sans-serif;
  color: var(--foreground);
  line-height: 1.6;
}

.spotlight-input::placeholder {
  color: var(--muted-foreground);
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

@keyframes placeholderFade {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.8;
  }
}

.spotlight-input:focus::placeholder {
  animation: placeholderFade 2s ease-in-out infinite;
}

.spotlight-button {
  position: absolute;
  right: 6px;
  bottom: 6px;
  padding: 0.75rem 1.5rem;
  background: var(--primary);
  color: var(--primary-foreground);
  border: 2px solid var(--border);
  border-radius: 0.375rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.spotlight-button:hover:not(:disabled) {
  transform: translate(-2px, -2px);
  box-shadow: 4px 4px 0 var(--border);
}

.spotlight-button:active:not(:disabled) {
  transform: translate(0, 0);
  box-shadow: var(--shadow-sm);
}

.spotlight-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Template cards */
.template-card {
  position: relative;
  overflow: hidden;
}

.template-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    transparent,
    rgba(var(--primary-rgb, 59, 130, 246), 0.05)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.template-card:hover::before {
  opacity: 1;
}

.template-card:hover {
  box-shadow: 6px 6px 0 var(--border);
  border-color: var(--primary);
}

/* Prompt cards */
.prompt-card {
  position: relative;
  overflow: hidden;
}

.prompt-card:hover {
  box-shadow: 4px 4px 0 var(--border);
  border-color: var(--primary);
}
