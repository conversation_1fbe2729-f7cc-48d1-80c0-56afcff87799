/* Sidebar Navigation Styles - Admin/Authenticated Section Only */

/* Note: Sidebar styles are primarily handled through CSS custom properties */
/* The sidebar component uses these variables defined in base/variables.css: */
/*
  --sidebar: Background color for sidebar
  --sidebar-foreground: Text color for sidebar
  --sidebar-primary: Primary accent color for active items
  --sidebar-primary-foreground: Text color for primary elements
  --sidebar-accent: Accent background color
  --sidebar-accent-foreground: Text color for accent elements
  --sidebar-border: Border color for sidebar elements
  --sidebar-ring: Ring/focus color for sidebar elements
*/

/* Additional sidebar-specific utility classes can be added here if needed */

/* Sidebar transition animations */
.sidebar-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Sidebar collapse/expand animations */
.sidebar-collapsed {
  width: 4.5rem;
}

.sidebar-expanded {
  width: 18rem;
}

/* Sidebar item hover effects */
.sidebar-item {
  transition: all 0.2s ease;
}

.sidebar-item:hover {
  background: var(--sidebar-accent);
  background-opacity: 0.2;
}

/* Sidebar active state */
.sidebar-item-active {
  background: var(--sidebar-primary);
  color: var(--sidebar-primary-foreground);
  border: 2px solid var(--sidebar-border);
  box-shadow: var(--shadow-sm);
}

/* Sidebar logo area */
.sidebar-logo {
  background: var(--sidebar-primary);
  color: var(--sidebar-primary-foreground);
  border: 2px solid var(--sidebar-border);
  box-shadow: var(--shadow-sm);
}

/* Sidebar settings area */
.sidebar-settings {
  border-top: 2px solid var(--sidebar-border);
  padding-top: 1rem;
}

/* Sidebar responsive behavior */
@media (max-width: 1024px) {
  .sidebar-mobile {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 50;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar-mobile.open {
    transform: translateX(0);
  }

  .sidebar-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 40;
  }
}
