/* Data Tables and Visualization Styles - Admin Section */

/* Data table specific styles */
.data-table {
  background: var(--card);
  border: 2px solid var(--border);
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: var(--shadow);
}

.data-table th {
  background: var(--muted);
  border-bottom: 2px solid var(--border);
  padding: 0.75rem 1rem;
  font-weight: 600;
  text-align: left;
}

.data-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border);
}

.data-table tr:hover {
  background: var(--muted);
  background-opacity: 0.5;
}

/* Data visualization components */
.chart-container {
  background: var(--card);
  border: 2px solid var(--border);
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: var(--shadow);
}

.metric-display {
  font-family: var(--font-mono);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary);
}

/* Loading states for data */
.data-loading {
  opacity: 0.6;
  pointer-events: none;
}

.data-loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--border);
  border-top: 2px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Data filters and controls */
.data-filter {
  background: var(--background);
  border: 2px solid var(--border);
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  transition: all 0.2s ease;
}

.data-filter:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 59, 130, 246), 0.1);
}

/* Export and action buttons for data */
.data-action-button {
  background: var(--secondary);
  color: var(--secondary-foreground);
  border: 2px solid var(--border);
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.data-action-button:hover {
  transform: translate(-1px, -1px);
  box-shadow: 2px 2px 0 var(--border);
}

/* Pagination controls */
.pagination {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.pagination-button {
  background: var(--background);
  border: 2px solid var(--border);
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.pagination-button:hover:not(:disabled) {
  background: var(--muted);
  transform: translate(-1px, -1px);
  box-shadow: 2px 2px 0 var(--border);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-button.active {
  background: var(--primary);
  color: var(--primary-foreground);
  border-color: var(--primary);
}

/* Search and filter inputs */
.data-search {
  position: relative;
  margin-bottom: 1rem;
}

.data-search input {
  width: 100%;
  background: var(--background);
  border: 2px solid var(--border);
  border-radius: 0.375rem;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  transition: all 0.2s ease;
}

.data-search input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb, 59, 130, 246), 0.1);
}

.data-search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--muted-foreground);
  pointer-events: none;
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-success {
  background: hsl(120 30% 90%);
  color: hsl(120 60% 25%);
  border: 1px solid hsl(120 40% 75%);
}

.status-warning {
  background: hsl(45 30% 90%);
  color: hsl(45 60% 25%);
  border: 1px solid hsl(45 40% 75%);
}

.status-error {
  background: hsl(0 30% 90%);
  color: hsl(0 60% 25%);
  border: 1px solid hsl(0 40% 75%);
}

.status-info {
  background: hsl(220 30% 90%);
  color: hsl(220 60% 25%);
  border: 1px solid hsl(220 40% 75%);
}

/* Responsive data tables */
@media (max-width: 768px) {
  .data-table {
    font-size: 0.875rem;
  }
  
  .data-table th,
  .data-table td {
    padding: 0.5rem;
  }
  
  .chart-container {
    padding: 1rem;
  }
  
  .metric-display {
    font-size: 1.25rem;
  }
}
