/* Marketing/Public Pages Specific Styles */

/* Warm Theme Typography */
.linear-heading {
  font-family: var(--font-sans);
  font-weight: 600;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.linear-body {
  font-family: var(--font-sans);
  font-weight: 400;
  line-height: 1.6;
  letter-spacing: -0.01em;
}

.linear-mono {
  font-family: var(--font-mono);
  font-weight: 500;
  letter-spacing: -0.01em;
}

/* Navigation - Warm Style */
.linear-nav {
  background: rgba(248, 244, 238, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border);
  box-shadow: var(--shadow-sm);
}

/* Cards - Warm Inspired */
.linear-card {
  background: var(--card);
  border: 1px solid var(--border);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow);
}

.linear-card:hover {
  border-color: var(--primary);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

/* Buttons - Warm Theme */
.linear-btn-primary {
  background: var(--primary);
  color: var(--primary-foreground);
  border: 1px solid var(--primary);
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow);
}

.linear-btn-primary:hover {
  background: hsl(15.1111 55.5556% 48%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.linear-btn-secondary {
  background: transparent;
  color: var(--foreground);
  border: 1px solid var(--border);
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-xs);
}

.linear-btn-secondary:hover {
  background: var(--muted);
  border-color: var(--primary);
  color: var(--foreground);
  box-shadow: var(--shadow);
}

/* Tags - Warm Color Usage */
.linear-tag {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-xs);
}

/* Green - Primary actions and success */
.linear-tag-green {
  background: hsl(120 30% 90%);
  border-color: hsl(120 40% 75%);
  color: hsl(120 60% 25%);
}

.linear-tag-green:hover {
  background: hsl(120 35% 85%);
  border-color: hsl(120 50% 65%);
  box-shadow: var(--shadow);
}

/* Purple - Premium features */
.linear-tag-purple {
  background: hsl(270 40% 90%);
  border-color: hsl(270 50% 75%);
  color: hsl(270 70% 30%);
}

/* Blue - Technical features (used sparingly) */
.linear-tag-blue {
  background: hsl(220 40% 90%);
  border-color: hsl(220 50% 75%);
  color: hsl(220 70% 30%);
}

/* Orange - Results and metrics (used sparingly) */
.linear-tag-orange {
  background: hsl(25 40% 90%);
  border-color: hsl(25 50% 75%);
  color: hsl(25 70% 30%);
}

/* Pink - Case studies (used very sparingly) */
.linear-tag-pink {
  background: hsl(330 40% 90%);
  border-color: hsl(330 50% 75%);
  color: hsl(330 70% 30%);
}

/* Data Visualization - Warm */
.linear-chart {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  padding: 2rem;
  box-shadow: var(--shadow);
}

.linear-metric {
  font-family: var(--font-mono);
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--primary);
}

/* Thin Lines & Engineering Aesthetic - Warm */
.linear-grid {
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.04) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.04) 1px, transparent 1px);
  background-size: 24px 24px;
}

.linear-divider {
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 50%,
    transparent 100%
  );
  margin: 3rem 0;
}

.linear-dotted-line {
  border-top: 1px dotted rgba(0, 0, 0, 0.15);
  margin: 1.5rem 0;
}

/* Team Cards - Warm */
.linear-team-card {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow);
}

.linear-team-card:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Lighting Effects - Warm */
.linear-glow {
  position: relative;
}

.linear-glow::before {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(
    135deg,
    rgba(218, 136, 103, 0.2) 0%,
    transparent 50%,
    rgba(218, 136, 103, 0.1) 100%
  );
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.linear-glow:hover::before {
  opacity: 1;
}

/* Scroll Animations - Refined */
.linear-fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.linear-fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Section Separators - Warm */
.linear-section-separator {
  height: 1px;
  background: radial-gradient(
    ellipse at center,
    rgba(218, 136, 103, 0.3) 0%,
    transparent 70%
  );
  margin: 4rem 0;
  position: relative;
}

.linear-section-separator::before {
  content: "";
  position: absolute;
  top: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: var(--primary);
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(218, 136, 103, 0.4);
}

/* Navigation styles */
.nav-blur {
  background: rgba(248, 244, 238, 0.9);
  border-bottom: 2px solid var(--border);
  backdrop-filter: blur(10px);
}

/* Enhanced sticky navigation */
.nav-hidden {
  transform: translateY(-100%);
}

/* Navigation link hover effects */
.nav-link {
  position: relative;
  padding: 0.25rem 0;
  color: var(--foreground);
  background: none;
  border: none;
  cursor: pointer;
}

/* Logo button styling */
.linear-heading button {
  background: none;
  border: none;
  padding: 0;
  font: inherit;
  text-align: left;
}

.nav-link::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 50%;
  background-color: var(--primary);
  transition:
    width 0.3s ease,
    left 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
  left: 0;
}

.nav-link:hover {
  color: var(--primary);
}
