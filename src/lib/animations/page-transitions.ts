import { animate } from "@motionone/dom"
import { browser } from "$app/environment"
import { prefersReducedMotion, ANIMATION_CONFIG } from "./index"

// Page transition types
export type TransitionType = "fade" | "slide" | "scale" | "blur" | "none"

// Page transition configuration
export const PAGE_TRANSITION_CONFIG = {
  duration: 0.3,
  easing: ANIMATION_CONFIG.easing.easeInOut,
  stagger: 0.05,
}

// Create page transition overlay
const createTransitionOverlay = () => {
  if (!browser) return null

  const overlay = document.createElement("div")
  overlay.id = "page-transition-overlay"
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--background, #ffffff);
    z-index: 9999;
    pointer-events: none;
    opacity: 0;
  `
  
  document.body.appendChild(overlay)
  return overlay
}

// Remove transition overlay
const removeTransitionOverlay = (overlay: HTMLElement) => {
  if (overlay && overlay.parentNode) {
    overlay.parentNode.removeChild(overlay)
  }
}

// Fade transition
export const fadeTransition = {
  out: (element: HTMLElement) => {
    if (!browser || prefersReducedMotion()) return Promise.resolve()
    
    return animate(
      element,
      { opacity: [1, 0] },
      {
        duration: PAGE_TRANSITION_CONFIG.duration,
        easing: PAGE_TRANSITION_CONFIG.easing,
      }
    ).finished
  },
  
  in: (element: HTMLElement) => {
    if (!browser || prefersReducedMotion()) {
      element.style.opacity = "1"
      return Promise.resolve()
    }
    
    element.style.opacity = "0"
    return animate(
      element,
      { opacity: [0, 1] },
      {
        duration: PAGE_TRANSITION_CONFIG.duration,
        easing: PAGE_TRANSITION_CONFIG.easing,
      }
    ).finished
  }
}

// Slide transition
export const slideTransition = {
  out: (element: HTMLElement, direction: "left" | "right" | "up" | "down" = "left") => {
    if (!browser || prefersReducedMotion()) return Promise.resolve()
    
    const transforms = {
      left: "translateX(-100%)",
      right: "translateX(100%)",
      up: "translateY(-100%)",
      down: "translateY(100%)"
    }
    
    return animate(
      element,
      {
        transform: ["translateX(0)", transforms[direction]],
        opacity: [1, 0.8]
      },
      {
        duration: PAGE_TRANSITION_CONFIG.duration,
        easing: PAGE_TRANSITION_CONFIG.easing,
      }
    ).finished
  },
  
  in: (element: HTMLElement, direction: "left" | "right" | "up" | "down" = "right") => {
    if (!browser || prefersReducedMotion()) {
      element.style.transform = "translateX(0)"
      element.style.opacity = "1"
      return Promise.resolve()
    }
    
    const transforms = {
      left: "translateX(-100%)",
      right: "translateX(100%)",
      up: "translateY(-100%)",
      down: "translateY(100%)"
    }
    
    element.style.transform = transforms[direction]
    element.style.opacity = "0.8"
    
    return animate(
      element,
      {
        transform: [transforms[direction], "translateX(0)"],
        opacity: [0.8, 1]
      },
      {
        duration: PAGE_TRANSITION_CONFIG.duration,
        easing: PAGE_TRANSITION_CONFIG.easing,
      }
    ).finished
  }
}

// Scale transition
export const scaleTransition = {
  out: (element: HTMLElement) => {
    if (!browser || prefersReducedMotion()) return Promise.resolve()
    
    return animate(
      element,
      {
        transform: ["scale(1)", "scale(0.95)"],
        opacity: [1, 0]
      },
      {
        duration: PAGE_TRANSITION_CONFIG.duration,
        easing: PAGE_TRANSITION_CONFIG.easing,
      }
    ).finished
  },
  
  in: (element: HTMLElement) => {
    if (!browser || prefersReducedMotion()) {
      element.style.transform = "scale(1)"
      element.style.opacity = "1"
      return Promise.resolve()
    }
    
    element.style.transform = "scale(1.05)"
    element.style.opacity = "0"
    
    return animate(
      element,
      {
        transform: ["scale(1.05)", "scale(1)"],
        opacity: [0, 1]
      },
      {
        duration: PAGE_TRANSITION_CONFIG.duration,
        easing: PAGE_TRANSITION_CONFIG.easing,
      }
    ).finished
  }
}

// Blur transition
export const blurTransition = {
  out: (element: HTMLElement) => {
    if (!browser || prefersReducedMotion()) return Promise.resolve()
    
    return animate(
      element,
      {
        filter: ["blur(0px)", "blur(10px)"],
        opacity: [1, 0]
      },
      {
        duration: PAGE_TRANSITION_CONFIG.duration,
        easing: PAGE_TRANSITION_CONFIG.easing,
      }
    ).finished
  },
  
  in: (element: HTMLElement) => {
    if (!browser || prefersReducedMotion()) {
      element.style.filter = "blur(0px)"
      element.style.opacity = "1"
      return Promise.resolve()
    }
    
    element.style.filter = "blur(10px)"
    element.style.opacity = "0"
    
    return animate(
      element,
      {
        filter: ["blur(10px)", "blur(0px)"],
        opacity: [0, 1]
      },
      {
        duration: PAGE_TRANSITION_CONFIG.duration,
        easing: PAGE_TRANSITION_CONFIG.easing,
      }
    ).finished
  }
}

// Main page transition function
export const pageTransition = async (
  type: TransitionType = "fade",
  direction?: "left" | "right" | "up" | "down"
) => {
  if (!browser || prefersReducedMotion() || type === "none") return

  const main = document.querySelector("main") || document.body
  if (!main) return

  // Create overlay for smooth transition
  const overlay = createTransitionOverlay()
  if (!overlay) return

  try {
    // Show overlay
    await animate(
      overlay,
      { opacity: [0, 1] },
      { duration: PAGE_TRANSITION_CONFIG.duration / 2 }
    ).finished

    // Apply transition based on type
    switch (type) {
      case "slide":
        await slideTransition.out(main as HTMLElement, direction)
        break
      case "scale":
        await scaleTransition.out(main as HTMLElement)
        break
      case "blur":
        await blurTransition.out(main as HTMLElement)
        break
      case "fade":
      default:
        await fadeTransition.out(main as HTMLElement)
        break
    }

    // Small delay for route change
    await new Promise(resolve => setTimeout(resolve, 50))

    // Transition in
    switch (type) {
      case "slide":
        await slideTransition.in(main as HTMLElement, direction)
        break
      case "scale":
        await scaleTransition.in(main as HTMLElement)
        break
      case "blur":
        await blurTransition.in(main as HTMLElement)
        break
      case "fade":
      default:
        await fadeTransition.in(main as HTMLElement)
        break
    }

    // Hide overlay
    await animate(
      overlay,
      { opacity: [1, 0] },
      { duration: PAGE_TRANSITION_CONFIG.duration / 2 }
    ).finished

  } finally {
    removeTransitionOverlay(overlay)
  }
}

// Staggered element animations for page load
export const staggerPageElements = (selector: string = ".animate-on-load") => {
  if (!browser || prefersReducedMotion()) return

  const elements = document.querySelectorAll(selector)
  
  elements.forEach((element, index) => {
    // Set initial state
    animate(
      element,
      {
        opacity: 0,
        transform: "translateY(20px)",
      },
      { duration: 0 }
    )

    // Animate in with stagger
    setTimeout(() => {
      animate(
        element,
        {
          opacity: 1,
          transform: "translateY(0px)",
        },
        {
          duration: PAGE_TRANSITION_CONFIG.duration * 2,
          easing: PAGE_TRANSITION_CONFIG.easing,
        }
      )
    }, index * (PAGE_TRANSITION_CONFIG.stagger * 1000))
  })
}

// Modal/component state transitions
export const componentTransitions = {
  // Modal enter/exit
  modal: {
    enter: (element: HTMLElement) => {
      if (!browser || prefersReducedMotion()) {
        element.style.opacity = "1"
        element.style.transform = "scale(1)"
        return Promise.resolve()
      }

      element.style.opacity = "0"
      element.style.transform = "scale(0.95)"

      return animate(
        element,
        {
          opacity: [0, 1],
          transform: ["scale(0.95)", "scale(1)"],
        },
        {
          duration: PAGE_TRANSITION_CONFIG.duration,
          easing: ANIMATION_CONFIG.easing.easeOut,
        }
      ).finished
    },

    exit: (element: HTMLElement) => {
      if (!browser || prefersReducedMotion()) return Promise.resolve()

      return animate(
        element,
        {
          opacity: [1, 0],
          transform: ["scale(1)", "scale(0.95)"],
        },
        {
          duration: PAGE_TRANSITION_CONFIG.duration,
          easing: ANIMATION_CONFIG.easing.easeIn,
        }
      ).finished
    }
  },

  // Dropdown enter/exit
  dropdown: {
    enter: (element: HTMLElement) => {
      if (!browser || prefersReducedMotion()) {
        element.style.opacity = "1"
        element.style.transform = "translateY(0) scale(1)"
        return Promise.resolve()
      }

      element.style.opacity = "0"
      element.style.transform = "translateY(-10px) scale(0.95)"

      return animate(
        element,
        {
          opacity: [0, 1],
          transform: ["translateY(-10px) scale(0.95)", "translateY(0) scale(1)"],
        },
        {
          duration: PAGE_TRANSITION_CONFIG.duration * 0.8,
          easing: ANIMATION_CONFIG.easing.easeOut,
        }
      ).finished
    },

    exit: (element: HTMLElement) => {
      if (!browser || prefersReducedMotion()) return Promise.resolve()

      return animate(
        element,
        {
          opacity: [1, 0],
          transform: ["translateY(0) scale(1)", "translateY(-10px) scale(0.95)"],
        },
        {
          duration: PAGE_TRANSITION_CONFIG.duration * 0.6,
          easing: ANIMATION_CONFIG.easing.easeIn,
        }
      ).finished
    }
  }
}
