import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '$lib/supabase/supabase.types'

type ContentDocument = Database['public']['Tables']['content_documents']['Row']
type ContentDocumentInsert = Database['public']['Tables']['content_documents']['Insert']
type ContentDocumentUpdate = Database['public']['Tables']['content_documents']['Update']

type ContentSession = Database['public']['Tables']['content_sessions']['Row']
type ContentSessionInsert = Database['public']['Tables']['content_sessions']['Insert']
type ContentSessionUpdate = Database['public']['Tables']['content_sessions']['Update']

type ContentCitation = Database['public']['Tables']['content_citations']['Row']
type ContentCitationInsert = Database['public']['Tables']['content_citations']['Insert']

export class ContentService {
  constructor(private supabase: SupabaseClient<Database>) {}

  // Document Operations
  async createDocument(data: ContentDocumentInsert): Promise<ContentDocument | null> {
    const { data: document, error } = await this.supabase
      .from('content_documents')
      .insert(data)
      .select()
      .single()

    if (error) {
      console.error('Error creating document:', error)
      throw new Error(`Failed to create document: ${error.message}`)
    }

    return document
  }

  async getDocument(documentId: string, userId: string): Promise<ContentDocument | null> {
    const { data: document, error } = await this.supabase
      .from('content_documents')
      .select('*')
      .eq('id', documentId)
      .eq('user_id', userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Document not found
      }
      console.error('Error fetching document:', error)
      throw new Error(`Failed to fetch document: ${error.message}`)
    }

    return document
  }

  async updateDocument(
    documentId: string,
    userId: string,
    updates: ContentDocumentUpdate
  ): Promise<ContentDocument | null> {
    const { data: document, error } = await this.supabase
      .from('content_documents')
      .update(updates)
      .eq('id', documentId)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      console.error('Error updating document:', error)
      throw new Error(`Failed to update document: ${error.message}`)
    }

    return document
  }

  async deleteDocument(documentId: string, userId: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('content_documents')
      .delete()
      .eq('id', documentId)
      .eq('user_id', userId)

    if (error) {
      console.error('Error deleting document:', error)
      throw new Error(`Failed to delete document: ${error.message}`)
    }

    return true
  }

  async getUserDocuments(
    userId: string,
    environmentId: string,
    options: {
      limit?: number
      offset?: number
      status?: string
      contentType?: string
      orderBy?: 'created_at' | 'updated_at' | 'title'
      orderDirection?: 'asc' | 'desc'
    } = {}
  ): Promise<ContentDocument[]> {
    const {
      limit = 50,
      offset = 0,
      status,
      contentType,
      orderBy = 'updated_at',
      orderDirection = 'desc'
    } = options

    let query = this.supabase
      .from('content_documents')
      .select('*')
      .eq('user_id', userId)
      .eq('environment_id', environmentId)

    if (status) {
      query = query.eq('status', status)
    }

    if (contentType) {
      query = query.eq('content_type', contentType)
    }

    query = query
      .order(orderBy, { ascending: orderDirection === 'asc' })
      .range(offset, offset + limit - 1)

    const { data: documents, error } = await query

    if (error) {
      console.error('Error fetching user documents:', error)
      throw new Error(`Failed to fetch documents: ${error.message}`)
    }

    return documents || []
  }

  async searchDocuments(
    userId: string,
    environmentId: string,
    searchTerm: string,
    options: {
      limit?: number
      contentType?: string
    } = {}
  ): Promise<ContentDocument[]> {
    const { limit = 20, contentType } = options

    let query = this.supabase
      .from('content_documents')
      .select('*')
      .eq('user_id', userId)
      .eq('environment_id', environmentId)
      .or(`title.ilike.%${searchTerm}%,content->>'text'.ilike.%${searchTerm}%`)

    if (contentType) {
      query = query.eq('content_type', contentType)
    }

    query = query
      .order('updated_at', { ascending: false })
      .limit(limit)

    const { data: documents, error } = await query

    if (error) {
      console.error('Error searching documents:', error)
      throw new Error(`Failed to search documents: ${error.message}`)
    }

    return documents || []
  }

  // Session Operations
  async createSession(data: ContentSessionInsert): Promise<ContentSession | null> {
    const { data: session, error } = await this.supabase
      .from('content_sessions')
      .insert(data)
      .select()
      .single()

    if (error) {
      console.error('Error creating session:', error)
      throw new Error(`Failed to create session: ${error.message}`)
    }

    return session
  }

  async getSession(sessionId: string, userId: string): Promise<ContentSession | null> {
    const { data: session, error } = await this.supabase
      .from('content_sessions')
      .select('*')
      .eq('id', sessionId)
      .eq('user_id', userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null
      }
      console.error('Error fetching session:', error)
      throw new Error(`Failed to fetch session: ${error.message}`)
    }

    return session
  }

  async updateSession(
    sessionId: string,
    userId: string,
    updates: ContentSessionUpdate
  ): Promise<ContentSession | null> {
    const { data: session, error } = await this.supabase
      .from('content_sessions')
      .update(updates)
      .eq('id', sessionId)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      console.error('Error updating session:', error)
      throw new Error(`Failed to update session: ${error.message}`)
    }

    return session
  }

  async getDocumentSessions(documentId: string, userId: string): Promise<ContentSession[]> {
    const { data: sessions, error } = await this.supabase
      .from('content_sessions')
      .select('*')
      .eq('document_id', documentId)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching document sessions:', error)
      throw new Error(`Failed to fetch sessions: ${error.message}`)
    }

    return sessions || []
  }

  // Citation Operations
  async addCitation(data: ContentCitationInsert): Promise<ContentCitation | null> {
    const { data: citation, error } = await this.supabase
      .from('content_citations')
      .insert(data)
      .select()
      .single()

    if (error) {
      console.error('Error adding citation:', error)
      throw new Error(`Failed to add citation: ${error.message}`)
    }

    return citation
  }

  async getDocumentCitations(documentId: string): Promise<ContentCitation[]> {
    const { data: citations, error } = await this.supabase
      .from('content_citations')
      .select('*')
      .eq('document_id', documentId)
      .order('position_in_content', { ascending: true })

    if (error) {
      console.error('Error fetching citations:', error)
      throw new Error(`Failed to fetch citations: ${error.message}`)
    }

    return citations || []
  }

  async updateCitation(
    citationId: string,
    updates: Partial<ContentCitationInsert>
  ): Promise<ContentCitation | null> {
    const { data: citation, error } = await this.supabase
      .from('content_citations')
      .update(updates)
      .eq('id', citationId)
      .select()
      .single()

    if (error) {
      console.error('Error updating citation:', error)
      throw new Error(`Failed to update citation: ${error.message}`)
    }

    return citation
  }

  async deleteCitation(citationId: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('content_citations')
      .delete()
      .eq('id', citationId)

    if (error) {
      console.error('Error deleting citation:', error)
      throw new Error(`Failed to delete citation: ${error.message}`)
    }

    return true
  }

  // Utility Methods
  async getDocumentStats(userId: string, environmentId: string) {
    const { data, error } = await this.supabase
      .from('content_documents')
      .select('status, content_type')
      .eq('user_id', userId)
      .eq('environment_id', environmentId)

    if (error) {
      console.error('Error fetching document stats:', error)
      throw new Error(`Failed to fetch document stats: ${error.message}`)
    }

    const stats = {
      total: data?.length || 0,
      byStatus: {} as Record<string, number>,
      byType: {} as Record<string, number>
    }

    data?.forEach(doc => {
      stats.byStatus[doc.status] = (stats.byStatus[doc.status] || 0) + 1
      stats.byType[doc.content_type] = (stats.byType[doc.content_type] || 0) + 1
    })

    return stats
  }
}
