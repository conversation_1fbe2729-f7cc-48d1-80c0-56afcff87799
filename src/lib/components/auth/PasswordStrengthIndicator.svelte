<script lang="ts">
  import { fade, fly, scale } from 'svelte/transition';
  import { cubicOut } from 'svelte/easing';
  import { CheckCircle2, XCircle, AlertCircle, Info } from 'lucide-svelte';

  export let password: string = '';
  export let showRequirements: boolean = true;
  
  // Check individual requirements
  $: lengthCheck = password.length >= 8 && password.length <= 16;
  $: hasLowercase = /[a-z]/.test(password);
  $: hasUppercase = /[A-Z]/.test(password);
  $: hasDigit = /\d/.test(password);
  
  // Calculate overall validity and strength
  $: checks = [lengthCheck, hasLowercase, hasUppercase, hasDigit];
  $: metRequirements = checks.filter(Boolean).length;
  $: isValid = metRequirements === 4;
  
  // Determine strength level and colors
  $: strengthLevel = 
    metRequirements === 0 ? 'empty' :
    metRequirements === 1 ? 'weak' :
    metRequirements === 2 ? 'fair' :
    metRequirements === 3 ? 'good' :
    'strong';
  
  $: strengthColor = 
    strengthLevel === 'empty' ? 'text-base-content/50' :
    strengthLevel === 'weak' ? 'text-error' :
    strengthLevel === 'fair' ? 'text-warning' :
    strengthLevel === 'good' ? 'text-info' :
    'text-success';
  
  $: progressColor = 
    strengthLevel === 'empty' ? 'progress-base-200' :
    strengthLevel === 'weak' ? 'progress-error' :
    strengthLevel === 'fair' ? 'progress-warning' :
    strengthLevel === 'good' ? 'progress-info' :
    'progress-success';
  
  // Character count progress
  $: charProgress = Math.min(password.length / 16 * 100, 100);
  $: charCountColor = 
    password.length === 0 ? 'text-base-content/50' :
    password.length < 8 ? 'text-error' :
    password.length <= 16 ? 'text-success' :
    'text-error';

  // Helper function to get icon component
  function getIcon(met: boolean, pending: boolean = false) {
    if (pending) return AlertCircle;
    return met ? CheckCircle2 : XCircle;
  }
  
  // Helper function to get icon color
  function getIconColor(met: boolean, pending: boolean = false) {
    if (pending) return 'text-base-content/30';
    return met ? 'text-success' : 'text-base-content/30';
  }
</script>

<div class="space-y-3">
  <!-- Character Count Progress Bar -->
  <div class="space-y-1">
    <div class="flex items-center justify-between text-sm">
      <span class="font-medium {charCountColor} transition-colors duration-200">
        Character length
      </span>
      <span class="{charCountColor} tabular-nums transition-colors duration-200">
        {password.length}/16
      </span>
    </div>
    <div class="relative">
      <progress 
        class="progress {progressColor} h-2 transition-all duration-300" 
        value={charProgress} 
        max="100"
      ></progress>
      <!-- Markers for min/max -->
      <div class="absolute top-0 left-[50%] w-px h-2 bg-base-content/20"></div>
      <div class="absolute -bottom-4 left-[50%] -translate-x-1/2 text-xs text-base-content/50">8</div>
    </div>
  </div>

  <!-- Strength Indicator -->
  {#if password.length > 0}
    <div transition:fade={{ duration: 200 }} class="flex items-center justify-between">
      <span class="text-sm font-medium {strengthColor} transition-colors duration-200">
        Password strength
      </span>
      <div class="flex items-center gap-1">
        {#each Array(4) as _, i}
          <div 
            class="h-1 w-8 rounded-full transition-all duration-300 {i < metRequirements ? 'bg-current' : 'bg-base-300'}"
            class:opacity-100={i < metRequirements}
            class:opacity-30={i >= metRequirements}
            style="color: var(--{strengthLevel === 'weak' ? 'er' : strengthLevel === 'fair' ? 'wa' : strengthLevel === 'good' ? 'in' : 'su'})"
          ></div>
        {/each}
        <span class="ml-2 text-sm {strengthColor} font-medium capitalize transition-colors duration-200">
          {strengthLevel}
        </span>
      </div>
    </div>
  {/if}

  <!-- Requirements Checklist -->
  {#if showRequirements && (password.length > 0 || showRequirements === 'always')}
    <div 
      transition:fly={{ y: -10, duration: 300, easing: cubicOut }}
      class="space-y-1.5 p-3 bg-base-200/50 rounded-lg"
    >
      <div class="flex items-center gap-2 text-sm font-medium text-base-content/70 mb-2">
        <Info size={14} />
        <span>Password requirements</span>
      </div>
      
      <!-- Length requirement -->
      <div class="flex items-center gap-2 text-sm transition-all duration-200">
        <svelte:component 
          this={getIcon(lengthCheck, password.length === 0)}
          size={16}
          class={getIconColor(lengthCheck, password.length === 0)}
        />
        <span class="{lengthCheck ? 'text-success' : 'text-base-content/70'} transition-colors duration-200">
          8-16 characters
        </span>
      </div>
      
      <!-- Lowercase requirement -->
      <div class="flex items-center gap-2 text-sm transition-all duration-200">
        <svelte:component 
          this={getIcon(hasLowercase, password.length === 0)}
          size={16}
          class={getIconColor(hasLowercase, password.length === 0)}
        />
        <span class="{hasLowercase ? 'text-success' : 'text-base-content/70'} transition-colors duration-200">
          One lowercase letter
        </span>
      </div>
      
      <!-- Uppercase requirement -->
      <div class="flex items-center gap-2 text-sm transition-all duration-200">
        <svelte:component 
          this={getIcon(hasUppercase, password.length === 0)}
          size={16}
          class={getIconColor(hasUppercase, password.length === 0)}
        />
        <span class="{hasUppercase ? 'text-success' : 'text-base-content/70'} transition-colors duration-200">
          One uppercase letter
        </span>
      </div>
      
      <!-- Digit requirement -->
      <div class="flex items-center gap-2 text-sm transition-all duration-200">
        <svelte:component 
          this={getIcon(hasDigit, password.length === 0)}
          size={16}
          class={getIconColor(hasDigit, password.length === 0)}
        />
        <span class="{hasDigit ? 'text-success' : 'text-base-content/70'} transition-colors duration-200">
          One number
        </span>
      </div>
    </div>
  {/if}
  
  <!-- Success message -->
  {#if isValid}
    <div 
      transition:scale={{ duration: 300, easing: cubicOut }}
      class="flex items-center gap-2 text-sm text-success"
    >
      <CheckCircle2 size={16} />
      <span class="font-medium">Strong password!</span>
    </div>
  {/if}
</div>

<style>
  /* Add subtle animations */
  :global(.progress) {
    @apply relative overflow-hidden;
  }
  
  :global(.progress::-webkit-progress-value) {
    @apply transition-all duration-300;
  }
  
  :global(.progress::-moz-progress-bar) {
    @apply transition-all duration-300;
  }
</style>