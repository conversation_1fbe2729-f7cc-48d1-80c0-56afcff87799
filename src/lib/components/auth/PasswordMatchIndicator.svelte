<script lang="ts">
  import { fade, scale } from "svelte/transition"
  import { CheckCircle2, XCircle, Eye, EyeOff } from "lucide-svelte"

  export let password: string = ""
  export let confirmPassword: string = ""
  export let showPassword: boolean = false

  $: hasInput = confirmPassword.length > 0
  $: isMatching = password === confirmPassword && hasInput
  $: isPartialMatch =
    password.startsWith(confirmPassword) && hasInput && !isMatching

  // Calculate how many characters match from the beginning
  $: matchingChars = (() => {
    let count = 0
    for (
      let i = 0;
      i < Math.min(password.length, confirmPassword.length);
      i++
    ) {
      if (password[i] === confirmPassword[i]) {
        count++
      } else {
        break
      }
    }
    return count
  })()

  $: matchPercentage =
    password.length > 0 ? (matchingChars / password.length) * 100 : 0
</script>

{#if hasInput}
  <div
    transition:fade={{ duration: 300 }}
    class="space-y-3 p-3 bg-gray-50 border border-gray-200 rounded-lg dark:bg-gray-950/20 dark:border-gray-800/30"
  >
    <!-- Match status -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-2 text-sm">
        {#if isMatching}
          <div
            class="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-lg text-green-700 font-medium dark:bg-green-950/20 dark:border-green-800/30 dark:text-green-400"
            transition:scale={{ duration: 400, start: 0.8 }}
          >
            <CheckCircle2 size={16} class="flex-shrink-0" />
            <span>Passwords match!</span>
          </div>
        {:else if isPartialMatch}
          <div
            class="flex items-center gap-2 p-2 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-700 font-medium dark:bg-yellow-950/20 dark:border-yellow-800/30 dark:text-yellow-400"
            transition:fade={{ duration: 300 }}
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              class="flex-shrink-0"
            >
              <circle cx="12" cy="12" r="10" />
              <path d="M12 6v6l4 2" />
            </svg>
            <span>Keep typing...</span>
          </div>
        {:else}
          <div
            class="flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded-lg text-red-700 font-medium dark:bg-red-950/20 dark:border-red-800/30 dark:text-red-400"
            transition:fade={{ duration: 300 }}
          >
            <XCircle size={16} class="flex-shrink-0" />
            <span>Passwords don't match</span>
          </div>
        {/if}
      </div>

      <!-- Toggle visibility hint -->
      {#if !isMatching && confirmPassword.length > 3}
        <button
          type="button"
          on:click={() => (showPassword = !showPassword)}
          class="flex items-center gap-1 text-xs text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 transition-colors p-1 rounded"
        >
          {#if showPassword}
            <EyeOff size={14} />
            <span>Hide</span>
          {:else}
            <Eye size={14} />
            <span>Show</span>
          {/if}
        </button>
      {/if}
    </div>

    <!-- Visual match indicator -->
    {#if !isMatching && confirmPassword.length > 0}
      <div class="space-y-1">
        <div class="flex justify-between text-xs text-base-content/60">
          <span>Match progress</span>
          <span>{matchingChars}/{password.length} characters</span>
        </div>
        <div class="relative h-1.5 bg-base-300 rounded-full overflow-hidden">
          <div
            class="absolute inset-y-0 left-0 bg-warning rounded-full transition-all duration-300"
            style="width: {matchPercentage}%"
          ></div>
        </div>
      </div>
    {/if}

    <!-- Character difference indicator for debugging -->
    {#if !isMatching && confirmPassword.length > password.length}
      <p class="text-xs text-base-content/60">
        Confirmation is {confirmPassword.length - password.length} character{confirmPassword.length -
          password.length !==
        1
          ? "s"
          : ""} longer
      </p>
    {/if}
  </div>
{/if}
