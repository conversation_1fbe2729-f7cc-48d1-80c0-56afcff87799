<script lang="ts">
  import { writable } from 'svelte/store'
  import { createEventDispatcher } from 'svelte'
  import { 
    Bot, 
    User, 
    Send, 
    Loader2, 
    X, 
    ChevronRight,
    FileText,
    Lightbulb,
    CheckCircle2,
    Sparkles,
    Search,
    Quote,
    PenTool,
    List
  } from 'lucide-svelte'
  import { slide, fade } from 'svelte/transition'

  export let isOpen: boolean = false
  export let documentId: string | null = null
  export let currentContent: string = ''
  export let envSlug: string

  const dispatch = createEventDispatcher()

  interface Message {
    id: string
    role: 'user' | 'assistant'
    content: string
    timestamp: Date
    action?: string
  }

  interface ProgressStep {
    id: number
    title: string
    description: string
    status: 'pending' | 'active' | 'completed'
    progress?: number
  }

  const messages = writable<Message[]>([])
  let input = ''
  let isLoading = false
  let progressSteps: ProgressStep[] = []
  let currentProgress = 0

  const quickActions = [
    {
      id: 'generate-outline',
      title: 'Generate Outline',
      description: 'Create a structured outline for your content',
      icon: List,
      prompt: 'Generate a detailed outline for this content topic'
    },
    {
      id: 'improve-writing',
      title: 'Improve Writing',
      description: 'Enhance grammar, style, and clarity',
      icon: PenTool,
      prompt: 'Please review and improve the grammar, style, and clarity of this content'
    },
    {
      id: 'summarize',
      title: 'Summarize',
      description: 'Create a concise summary',
      icon: FileText,
      prompt: 'Please create a concise summary of this content'
    },
    {
      id: 'research',
      title: 'Research Topic',
      description: 'Find relevant information and sources',
      icon: Search,
      prompt: 'Research this topic and provide relevant information with sources'
    },
    {
      id: 'add-citations',
      title: 'Add Citations',
      description: 'Generate proper citations for sources',
      icon: Quote,
      prompt: 'Help me add proper citations to this content'
    },
    {
      id: 'brainstorm',
      title: 'Brainstorm Ideas',
      description: 'Generate creative ideas and suggestions',
      icon: Lightbulb,
      prompt: 'Help me brainstorm ideas to expand on this content'
    }
  ]

  function generateId(): string {
    return Math.random().toString(36).substr(2, 9)
  }

  async function sendMessage(messageText?: string, action?: string) {
    const userMessage = messageText || input.trim()
    if (!userMessage || isLoading) return

    input = ''
    isLoading = true
    progressSteps = []
    currentProgress = 0

    // Add user message to chat
    messages.update(msgs => [
      ...msgs,
      {
        id: generateId(),
        role: 'user',
        content: userMessage,
        timestamp: new Date(),
        action
      }
    ])

    try {
      // Use streaming endpoint
      const response = await fetch(
        `/dashboard/${envSlug}/content-agent?stream=true`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            message: userMessage, 
            documentId,
            action,
            currentContent 
          })
        }
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // Handle streaming response
      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (!reader) {
        throw new Error('No response body')
      }

      let assistantMessage = ''
      let assistantMessageId = generateId()

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              
              if (data.step !== undefined) {
                // Update progress
                currentProgress = data.progress || 0
                
                // Update or add progress step
                const existingStepIndex = progressSteps.findIndex(s => s.id === data.step)
                if (existingStepIndex >= 0) {
                  progressSteps[existingStepIndex] = {
                    ...progressSteps[existingStepIndex],
                    status: data.status,
                    progress: data.progress
                  }
                } else {
                  progressSteps = [...progressSteps, {
                    id: data.step,
                    title: data.action,
                    description: data.action,
                    status: data.status,
                    progress: data.progress
                  }]
                }
              }

              if (data.response) {
                assistantMessage = data.response
              }

              if (data.status === 'completed' && assistantMessage) {
                // Add assistant response to messages
                messages.update(msgs => [
                  ...msgs,
                  {
                    id: assistantMessageId,
                    role: 'assistant',
                    content: assistantMessage,
                    timestamp: new Date(),
                    action
                  }
                ])

                // Dispatch content update if it's a generation action
                if (action === 'generate-outline' || action === 'generate-content') {
                  dispatch('contentGenerated', { content: assistantMessage, action })
                }
              }

              if (data.status === 'error') {
                throw new Error(data.error || 'Unknown error occurred')
              }
            } catch (parseError) {
              console.warn('Failed to parse SSE data:', parseError)
            }
          }
        }
      }
    } catch (error) {
      console.error('Error sending message:', error)
      messages.update(msgs => [
        ...msgs,
        {
          id: generateId(),
          role: 'assistant',
          content: 'I apologize, but I encountered an error while processing your request. Please try again.',
          timestamp: new Date()
        }
      ])
    } finally {
      isLoading = false
      progressSteps = []
      currentProgress = 0
    }
  }

  function handleQuickAction(action: typeof quickActions[0]) {
    let prompt = action.prompt
    
    // Add context if we have current content
    if (currentContent && currentContent.trim()) {
      prompt += `\n\nCurrent content:\n${currentContent}`
    }
    
    sendMessage(prompt, action.id)
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      sendMessage()
    }
  }

  function formatContent(content: string): string {
    // Basic markdown to HTML conversion
    return content
      .replace(/^### (.+)$/gm, '<h3 class="text-lg font-bold mt-4 mb-2">$1</h3>')
      .replace(/^## (.+)$/gm, '<h2 class="text-xl font-bold mt-6 mb-3">$1</h2>')
      .replace(/^# (.+)$/gm, '<h1 class="text-2xl font-bold mb-4">$1</h1>')
      .replace(/^\* (.+)$/gm, '<li class="ml-4">• $1</li>')
      .replace(/^- (.+)$/gm, '<li class="ml-4">• $1</li>')
      .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.+?)\*/g, '<em>$1</em>')
      .replace(/\n\n/g, '</p><p class="mb-4">')
      .replace(/^/, '<p class="mb-4">')
      .replace(/$/, '</p>')
  }

  function closeSidebar() {
    isOpen = false
    dispatch('close')
  }
</script>

{#if isOpen}
  <div 
    class="fixed inset-y-0 right-0 w-96 bg-card border-l border-border shadow-lg z-50 flex flex-col"
    transition:slide={{ duration: 300, axis: 'x' }}
  >
    <!-- Header -->
    <div class="flex items-center justify-between p-4 border-b border-border">
      <div class="flex items-center gap-2">
        <Bot class="w-5 h-5 text-primary" />
        <h2 class="font-semibold">Content Agent</h2>
      </div>
      <button
        on:click={closeSidebar}
        class="p-1 hover:bg-muted rounded transition-colors"
      >
        <X class="w-4 h-4" />
      </button>
    </div>

    <!-- Quick Actions -->
    {#if $messages.length === 0}
      <div class="p-4 border-b border-border">
        <h3 class="text-sm font-medium mb-3">Quick Actions</h3>
        <div class="grid grid-cols-2 gap-2">
          {#each quickActions as action}
            <button
              on:click={() => handleQuickAction(action)}
              class="p-3 text-left border border-border rounded hover:bg-muted transition-colors group"
              disabled={isLoading}
            >
              <div class="flex items-center gap-2 mb-1">
                <svelte:component this={action.icon} class="w-4 h-4 text-primary" />
                <span class="text-sm font-medium">{action.title}</span>
              </div>
              <p class="text-xs text-muted-foreground">{action.description}</p>
            </button>
          {/each}
        </div>
      </div>
    {/if}

    <!-- Messages -->
    <div class="flex-1 overflow-y-auto p-4 space-y-4">
      {#each $messages as message}
        <div class="flex gap-3 {message.role === 'user' ? 'flex-row-reverse' : ''}">
          <div class="w-8 h-8 flex-shrink-0 flex items-center justify-center rounded-full border-2 border-border bg-{message.role === 'user' ? 'primary' : 'secondary'}">
            {#if message.role === 'user'}
              <User class="w-4 h-4 text-primary-foreground" />
            {:else}
              <Bot class="w-4 h-4 text-secondary-foreground" />
            {/if}
          </div>
          <div class="flex-1 {message.role === 'user' ? 'text-right' : ''}">
            <div class="inline-block max-w-full p-3 rounded-lg {message.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'}">
              {#if message.role === 'assistant'}
                <div class="prose prose-sm max-w-none">
                  {@html formatContent(message.content)}
                </div>
              {:else}
                <p class="text-sm">{message.content}</p>
              {/if}
            </div>
            <p class="text-xs text-muted-foreground mt-1">
              {message.timestamp.toLocaleTimeString()}
            </p>
          </div>
        </div>
      {/each}

      <!-- Progress Steps -->
      {#if isLoading && progressSteps.length > 0}
        <div class="space-y-2" transition:fade>
          {#each progressSteps as step}
            <div class="flex items-center gap-2 p-2 rounded bg-muted/50">
              {#if step.status === 'completed'}
                <CheckCircle2 class="w-4 h-4 text-green-500" />
              {:else if step.status === 'active'}
                <Loader2 class="w-4 h-4 animate-spin text-primary" />
              {:else}
                <div class="w-4 h-4 rounded-full border-2 border-muted-foreground/30"></div>
              {/if}
              <span class="text-sm {step.status === 'completed' ? 'text-muted-foreground' : 'text-foreground'}">{step.title}</span>
            </div>
          {/each}
        </div>
      {/if}
    </div>

    <!-- Input -->
    <div class="p-4 border-t border-border">
      <div class="flex gap-2">
        <textarea
          bind:value={input}
          on:keydown={handleKeyDown}
          placeholder="Ask the Content Agent for help..."
          class="flex-1 resize-none border border-border rounded px-3 py-2 text-sm bg-background focus:outline-none focus:ring-2 focus:ring-primary"
          rows="2"
          disabled={isLoading}
        ></textarea>
        <button
          on:click={() => sendMessage()}
          disabled={!input.trim() || isLoading}
          class="px-3 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {#if isLoading}
            <Loader2 class="w-4 h-4 animate-spin" />
          {:else}
            <Send class="w-4 h-4" />
          {/if}
        </button>
      </div>
    </div>
  </div>
{/if}
