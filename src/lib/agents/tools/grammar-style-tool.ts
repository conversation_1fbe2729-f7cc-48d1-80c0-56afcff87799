import { z } from "zod"
import { createTool } from "@mastra/core"

const grammarStyleSchema = z.object({
  inputText: z
    .string()
    .min(10)
    .describe("The text content to be checked and corrected"),
  correctionType: z
    .enum([
      "grammar-only",
      "style-only", 
      "comprehensive",
      "tone-adjustment",
      "clarity-improvement",
      "conciseness"
    ])
    .default("comprehensive")
    .describe("Type of corrections to apply"),
  targetTone: z
    .enum([
      "professional",
      "casual",
      "friendly",
      "formal",
      "conversational", 
      "authoritative",
      "persuasive",
      "academic"
    ])
    .default("professional")
    .describe("Desired tone for the text"),
  writingStyle: z
    .enum([
      "business",
      "academic",
      "creative",
      "technical",
      "journalistic",
      "marketing",
      "legal",
      "medical"
    ])
    .default("business")
    .describe("Writing style context for corrections"),
  targetAudience: z
    .enum([
      "general",
      "technical",
      "executive",
      "academic",
      "beginner",
      "expert"
    ])
    .default("general")
    .describe("Target audience for the content"),
  preserveVoice: z
    .boolean()
    .default(true)
    .describe("Whether to preserve the original author's voice and style"),
  suggestAlternatives: z
    .boolean()
    .default(true)
    .describe("Whether to provide alternative phrasings for improvements"),
  checkReadability: z
    .boolean()
    .default(true)
    .describe("Whether to analyze and improve readability"),
  fixPassiveVoice: z
    .boolean()
    .default(false)
    .describe("Whether to convert passive voice to active voice where appropriate")
})

export const grammarStyleTool = createTool({
  id: "grammar-style-correction",
  description: "Check and correct grammar, style, tone, and readability of text content",
  inputSchema: grammarStyleSchema,
  execute: async (context) => {
    const {
      inputText,
      correctionType,
      targetTone,
      writingStyle,
      targetAudience,
      preserveVoice,
      suggestAlternatives,
      checkReadability,
      fixPassiveVoice
    } = context.context

    // Analyze input text characteristics
    const wordCount = inputText.split(/\s+/).length
    const sentenceCount = inputText.split(/[.!?]+/).filter(s => s.trim().length > 0).length
    const avgWordsPerSentence = wordCount / sentenceCount
    const paragraphCount = inputText.split(/\n\s*\n/).filter(p => p.trim().length > 0).length

    // Define correction type instructions
    const correctionInstructions = {
      "grammar-only": "Focus only on grammatical errors, punctuation, and spelling mistakes",
      "style-only": "Focus on style improvements, word choice, and sentence structure",
      "comprehensive": "Address grammar, style, clarity, and overall writing quality",
      "tone-adjustment": "Adjust the tone and voice to match the target tone",
      "clarity-improvement": "Focus on making the text clearer and easier to understand",
      "conciseness": "Make the text more concise while preserving meaning"
    }

    // Define tone characteristics
    const toneCharacteristics = {
      professional: "Clear, respectful, competent, and business-appropriate",
      casual: "Relaxed, informal, approachable, and conversational",
      friendly: "Warm, welcoming, personable, and engaging",
      formal: "Structured, traditional, respectful, and ceremonial",
      conversational: "Natural, dialogue-like, engaging, and personal",
      authoritative: "Confident, knowledgeable, decisive, and commanding",
      persuasive: "Compelling, convincing, motivating, and influential",
      academic: "Scholarly, precise, objective, and research-oriented"
    }

    // Define style guidelines
    const styleGuidelines = {
      business: "Clear, concise, action-oriented, and results-focused",
      academic: "Formal, precise, evidence-based, and objective",
      creative: "Expressive, imaginative, engaging, and original",
      technical: "Precise, detailed, logical, and specification-focused",
      journalistic: "Factual, balanced, clear, and informative",
      marketing: "Persuasive, benefit-focused, engaging, and action-oriented",
      legal: "Precise, formal, comprehensive, and unambiguous",
      medical: "Accurate, professional, clear, and patient-focused"
    }

    // Build the correction prompt
    let correctionPrompt = `Review and improve the following text:

ORIGINAL TEXT:
${inputText}

CORRECTION REQUIREMENTS:
- Correction Type: ${correctionType} - ${correctionInstructions[correctionType]}
- Target Tone: ${targetTone} - ${toneCharacteristics[targetTone]}
- Writing Style: ${writingStyle} - ${styleGuidelines[writingStyle]}
- Target Audience: ${targetAudience}
- Preserve Voice: ${preserveVoice ? 'Yes - maintain author\'s unique style' : 'No - prioritize clarity and correctness'}
- Suggest Alternatives: ${suggestAlternatives ? 'Yes' : 'No'}
- Check Readability: ${checkReadability ? 'Yes' : 'No'}
- Fix Passive Voice: ${fixPassiveVoice ? 'Yes' : 'No'}

INSTRUCTIONS:
1. ${correctionInstructions[correctionType]}
2. Ensure the tone is ${targetTone}: ${toneCharacteristics[targetTone]}
3. Follow ${writingStyle} writing style: ${styleGuidelines[writingStyle]}
4. Write for ${targetAudience} audience level`

    if (preserveVoice) {
      correctionPrompt += `\n5. Preserve the author's unique voice and personality`
    }

    if (checkReadability) {
      correctionPrompt += `\n6. Improve readability and sentence flow`
    }

    if (fixPassiveVoice) {
      correctionPrompt += `\n7. Convert passive voice to active voice where appropriate`
    }

    if (suggestAlternatives) {
      correctionPrompt += `\n\nFor significant changes, provide the original phrase and your suggested improvement with a brief explanation.`
    }

    // Add specific guidance based on correction type
    if (correctionType === "conciseness") {
      correctionPrompt += `\n\nFocus on eliminating redundancy, wordiness, and unnecessary phrases while preserving meaning.`
    } else if (correctionType === "clarity-improvement") {
      correctionPrompt += `\n\nSimplify complex sentences, clarify ambiguous statements, and improve logical flow.`
    } else if (correctionType === "tone-adjustment") {
      correctionPrompt += `\n\nAdjust word choice, sentence structure, and phrasing to achieve the ${targetTone} tone.`
    }

    correctionPrompt += `\n\nProvide the corrected text now:`

    // Calculate readability metrics (simplified)
    const readabilityMetrics = {
      avgWordsPerSentence,
      avgSentencesPerParagraph: sentenceCount / paragraphCount,
      readabilityLevel: avgWordsPerSentence < 15 ? 'Easy' : avgWordsPerSentence < 20 ? 'Moderate' : 'Difficult'
    }

    // Identify potential issues
    const potentialIssues = []
    if (avgWordsPerSentence > 25) potentialIssues.push("Long sentences may reduce readability")
    if (sentenceCount / paragraphCount > 8) potentialIssues.push("Paragraphs may be too long")
    if (inputText.includes("  ")) potentialIssues.push("Multiple spaces detected")
    if (inputText.match(/\b(very|really|quite|rather)\b/gi)) potentialIssues.push("Weak intensifiers detected")

    return {
      success: true,
      inputAnalysis: {
        wordCount,
        sentenceCount,
        paragraphCount,
        avgWordsPerSentence: Math.round(avgWordsPerSentence * 10) / 10,
        readabilityMetrics,
        potentialIssues
      },
      correctionConfig: {
        correctionType,
        targetTone,
        writingStyle,
        targetAudience,
        preserveVoice,
        suggestAlternatives,
        checkReadability,
        fixPassiveVoice
      },
      correctionPrompt,
      metadata: {
        toneDescription: toneCharacteristics[targetTone],
        styleDescription: styleGuidelines[writingStyle],
        correctionFocus: correctionInstructions[correctionType],
        estimatedImprovements: potentialIssues.length
      }
    }
  }
})
