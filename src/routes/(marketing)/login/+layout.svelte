<script lang="ts">
  let { children } = $props()
  let isEurope = $state(false)
  try {
    isEurope = Intl.DateTimeFormat()
      .resolvedOptions()
      .timeZone.startsWith("Europe/")
  } catch (e) {
    /* continue */
  }
</script>

<div
  class="content-center max-w-2xl mx-auto min-h-[70vh] pb-12 flex items-center place-content-center px-4"
>
  <div class="flex flex-col w-full max-w-[500px]">
    <div class="card-brutal p-8 mb-8">
      {@render children()}
    </div>
    <div class="mt-8 {isEurope ? 'block' : 'hidden'} text-center">
      <span class="text-sm font-bold text-muted-foreground"
        >🍪 Logging in uses Cookies 🍪</span
      >
    </div>
  </div>
</div>
